<template>
  <div id="app">
    <DocumentPage />
  </div>
</template>

<script setup>
import DocumentPage from './views/DocumentPage.vue'
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
}

body {
  margin: 0;
  background-color: #f5f5f5;
  overflow-x: hidden;
}

.p-6 {
  padding: 24px;
}

@media (max-width: 768px) {
  .p-6 {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .p-6 {
    padding: 12px;
  }
}

.d-flex {
  display: flex;
}

.justify-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}

.mb-3 {
  margin-bottom: 12px;
}

.mr-10 {
  margin-right: 40px;
}

.mt-6 {
  margin-top: 24px;
}

.gap-3 {
  gap: 12px;
}

.mr-2 {
  margin-right: 8px;
}

body{
  background-color: #F9F8F5
}
</style>