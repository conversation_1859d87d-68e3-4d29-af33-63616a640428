// main.js
import { createApp } from "vue"
import App from "./App.vue"
import '@mdi/font/css/materialdesignicons.css'
// Vuetify
import "vuetify/styles"
import { createVuetify } from "vuetify"
import * as components from "vuetify/components"
import * as directives from "vuetify/directives"
import '@fortawesome/fontawesome-free/css/all.css'

const vuetify = createVuetify({
  components,
  directives,
})

createApp(App).use(vuetify).mount("#app")
