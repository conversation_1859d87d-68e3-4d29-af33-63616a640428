.document-generator-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.tabs-container {
  background-color: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 0 40px;
  box-sizing: border-box;
}

.tabs {
  display: flex;
  gap: 0;
}

.tab {
  padding: 16px 24px;
  background: none;
  border: none;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.tab.active {
  color: #28a745;
  border-bottom-color: #28a745;
  background-color: #f0fdf4;
}

.pagination-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background-color: white;
  flex-wrap: wrap;
  gap: 16px;
  box-sizing: border-box;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  min-width: 36px;
  transition: all 0.2s ease;
}

.pagination-btn:hover {
  background-color: #f8f9fa;
}

.pagination-btn.active {
  background-color: #28a745;
  color: white;
  border-color: #28a745;
}

.pagination-dots {
  color: #6c757d;
  padding: 0 8px;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 16px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
}

.dropdown-arrow {
  font-size: 10px;
  color: #6c757d;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.add-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.add-btn:hover {
  background-color: #218838;
}

.refresh-btn {
  background: none;
  border: 1px solid #dee2e6;
  color: #6c757d;
  padding: 10px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background-color: #f8f9fa;
}

.table-container {
  background-color: white;
  margin: 0 40px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  box-sizing: border-box;
}

.document-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.document-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.document-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: top;
}

.document-table tr:last-child td {
  border-bottom: none;
}

.document-table tr:hover {
  background-color: #f8f9fa;
}

.employee-info, .candidate-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.employee-name, .candidate-name {
  color: #343a40;
  font-weight: 500;
  font-size: 14px;
}

.employee-id, .candidate-id {
  color: #6c757d;
  font-size: 12px;
}

.document-name {
  color: #28a745;
  font-weight: 500;
}

.signatories {
  display: flex;
  gap: 8px;
}

.signatory-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.signatory-badge.primary {
  background-color: #28a745;
}

.signatory-badge.secondary {
  background-color: #17a2b8;
}

.status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #28a745;
  color: white;
  border-radius: 50%;
  font-size: 12px;
}

.status-text {
  color: #28a745;
  font-weight: 500;
}

.document-author {
  color: #6c757d;
  font-size: 13px;
}

.action-menu {
  position: relative;
  display: inline-block;
}

.menu-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

.menu-dropdown {
  position: absolute;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-radius: 6px;
  min-width: 120px;
  box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
  z-index: 100;
}

.menu-item {
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
}

.menu-item:hover {
  background: #f5f5f5;
}

/* Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .tabs-container {
    padding: 0 20px;
  }

  .pagination-header {
    padding: 16px 20px;
  }

  .table-container {
    margin: 0 20px;
  }

  .tab {
    padding: 14px 20px;
    font-size: 13px;
  }

  .pagination-btn {
    padding: 6px 10px;
    font-size: 13px;
    min-width: 32px;
  }

  .add-btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  .refresh-btn {
    padding: 8px 10px;
    font-size: 14px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .document-generator-container {
    padding: 0;
  }

  .tabs-container {
    padding: 0 12px;
  }

  .tabs {
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }

  .tab {
    padding: 12px 16px;
    font-size: 12px;
    flex: 1;
    text-align: center;
    min-width: 0;
  }

  .pagination-header {
    padding: 12px;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .pagination-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .table-container {
    margin: 0 12px;
    border-radius: 4px;
  }

  .document-table {
    font-size: 13px;
  }

  .document-table th {
    padding: 12px 8px;
    font-size: 11px;
  }

  .document-table td {
    padding: 12px 8px;
  }

  .employee-info, .candidate-info {
    gap: 2px;
  }

  .employee-name, .candidate-name {
    font-size: 13px;
  }

  .employee-id, .candidate-id {
    font-size: 11px;
  }

  .document-name {
    font-size: 13px;
  }

  .signatory-badge {
    width: 24px;
    height: 24px;
    font-size: 9px;
  }

  .status-icon {
    width: 18px;
    height: 18px;
    font-size: 10px;
  }

  .status-text {
    font-size: 13px;
  }

  .document-author {
    font-size: 12px;
  }

  .add-btn {
    padding: 10px 16px;
    font-size: 12px;
  }

  .refresh-btn {
    padding: 10px;
    font-size: 14px;
  }

  .pagination-btn {
    padding: 8px 10px;
    font-size: 12px;
    min-width: 30px;
  }

  .items-per-page {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .tabs-container {
    padding: 0 8px;
  }

  .tab {
    padding: 10px 12px;
    font-size: 11px;
  }

  .pagination-header {
    padding: 8px;
  }

  .table-container {
    margin: 0 8px;
  }

  .document-table th {
    padding: 8px 4px;
    font-size: 10px;
  }

  .document-table td {
    padding: 8px 4px;
  }

  .employee-name, .candidate-name {
    font-size: 12px;
  }

  .employee-id, .candidate-id {
    font-size: 10px;
  }

  .document-name {
    font-size: 12px;
  }

  .signatory-badge {
    width: 20px;
    height: 20px;
    font-size: 8px;
  }

  .status-icon {
    width: 16px;
    height: 16px;
    font-size: 9px;
  }

  .status-text {
    font-size: 12px;
  }

  .document-author {
    font-size: 11px;
  }

  .add-btn {
    padding: 8px 12px;
    font-size: 11px;
  }

  .refresh-btn {
    padding: 8px;
    font-size: 12px;
  }

  .pagination-btn {
    padding: 6px 8px;
    font-size: 11px;
    min-width: 28px;
  }

  .items-per-page {
    padding: 6px 8px;
    font-size: 11px;
  }

  .pagination-controls {
    gap: 4px;
  }

  .action-buttons {
    gap: 8px;
  }
}