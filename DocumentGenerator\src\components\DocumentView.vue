<template>
  <div class="details-full-view">
    <!-- Header -->
    <div class="details-header">
      <h2>{{ item.document }}</h2>
      <div class="header-actions">
        <v-btn 
          variant="flat" 
          size="small"
          @click="handleClose"
          class="back-btn"
        >
          <v-icon size="30px">mdi-close</v-icon>
        </v-btn>
      </div>
    </div>

    <!-- Content -->
    <div class="details-content-full">
      <div class="document-letter">
        <!-- Letter Header -->
        <div class="letter-header">
          <div class="company-logo">
            <div class="logo-placeholder">PL</div>
            <span class="company-name">PREMIUM</span>
          </div>
        </div>

        <!-- Letter Body -->
        <div class="letter-body">
          <!-- Candidate Info Box -->
          <div class="candidate-info-box">
            <table class="info-table">
              <tr>
                <td><strong>Candidate Name:</strong></td>
                <td>{{ item.candidate }}</td>
              </tr>
              <tr>
                <td><strong>Email Id:</strong></td>
                <td>{{ item.email }}</td>
              </tr>
              <tr>
                <td><strong>Designation:</strong></td>
                <td>{{ item.designation }}</td>
              </tr>
              <tr>
                <td><strong>Department:</strong></td>
                <td>{{ item.department }}</td>
              </tr>
              <tr>
                <td><strong>Mobile Number:</strong></td>
                <td>{{ item.mobile }}</td>
              </tr>
            </table>
          </div>

          <!-- Letter Content -->
          <div class="letter-content">
            <p><strong>Dear {{ item.candidate }},</strong></p>
            
            <p>The annual starting salary for this position is <strong>[$CUR $AMOUNT]</strong> to be paid on a <strong>[$MONTHLY, $BI-MONTHLY, $WEEKLY, $BI-]</strong> basis by <strong>[DIRECT DEPOSIT, CHECK, ETC.]</strong>, starting on <strong>[FIRST PAY PERIOD]</strong>. In addition to this starting salary, we're offering you <strong>[$HOLIDAY $$$$$ OPTIONS, $$TUITION REIMBURSEMENT STRUCTURE, ETC. — IF APPLICABLE]</strong>.</p>
            
            <p>Please confirm your acceptance of this offer by signing and returning this letter by <strong>[OFFER EXPIRATION DATE]</strong>.</p>
            
            <p>We are excited to have you join our team! If you have any questions, please feel free to reach out at any time.</p>
          </div>

          <!-- Signature Section -->
          <div class="signature-section">
            <div class="signature-left">
              <p>{{ item.candidate }}</p>
              <p>{{ item.date || '15/05/2024 12:43:22(+05:30)' }}</p>
            </div>
            <div class="signature-right">
              <p>{{ item.signatory || 'gwon R' }}</p>
              <p>{{ item.signatoryTitle || '(Date Signed (Manager / Admin))' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.details-full-view {
  width: 100%;
  max-width: 1700px;
  min-height: 100vh;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  margin: 50px auto 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 3px solid #e4e9e7;
  background: #ffffff;
  flex-wrap: wrap;
  gap: 12px;
}

.details-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.4rem;
  font-weight: 600;
}

.back-btn {
  color: #c3c5c5;
 
}

.back-btn:hover {
  background-color: #ffffff;
  color: #26f5b0;;
}

.details-content-full {
  min-height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 24px;
}

.document-letter {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  background: #fff;
  padding: 40px;
  line-height: 1.6;
  font-family: 'Arial', sans-serif;
  border-radius: 8px;
  box-sizing: border-box;
}

.letter-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 2px solid #26f5b0;
}

.company-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 10px;
}

.logo-placeholder {
  width: 50px;
  height: 50px;
  background: #26f5b0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  border-radius: 4px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.candidate-info-box {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin: 30px 0;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
}

.info-table td {
  padding: 8px 0;
  vertical-align: top;
}

.info-table td:first-child {
  width: 30%;
  color: #666;
}

.info-table td:last-child {
  color: #333;
  padding-left: 20px;
}

.letter-content {
  margin: 30px 0;
  color: #444;
  text-align: justify;
}

.letter-content p {
  margin: 20px 0;
}

.signature-section {
  display: flex;
  justify-content: space-between;
  margin-top: 60px;
  padding-top: 40px;
  border-top: 1px solid #e0e0e0;
}

.signature-left,
.signature-right {
  text-align: center;
  min-width: 200px;
}

.signature-left p,
.signature-right p {
  margin: 5px 0;
  color: #666;
}

/* Tablet styles */
@media (max-width: 1024px) {
  .details-full-view {
    margin: 20px auto 0;
    border-radius: 0;
  }

  .document-letter {
    padding: 30px;
  }

  .details-content-full {
    padding: 20px;
  }

  .details-header {
    padding: 16px 20px;
  }

  .details-header h2 {
    font-size: 1.2rem;
  }

  .company-name {
    font-size: 20px;
  }

  .logo-placeholder {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .details-full-view {
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
  }

  .document-letter {
    padding: 20px;
  }

  .signature-section {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .details-content-full {
    padding: 16px;
    min-height: calc(100vh - 80px);
  }

  .details-header {
    padding: 12px 16px;
  }

  .details-header h2 {
    font-size: 1.1rem;
  }

  .company-logo {
    flex-direction: column;
    gap: 8px;
  }

  .company-name {
    font-size: 18px;
  }

  .logo-placeholder {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .candidate-info-box {
    padding: 16px;
    margin: 20px 0;
  }

  .info-table td:first-child {
    width: 40%;
    font-size: 13px;
  }

  .info-table td:last-child {
    padding-left: 12px;
    font-size: 13px;
  }

  .letter-content {
    font-size: 14px;
  }

  .signature-left,
  .signature-right {
    min-width: auto;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .details-content-full {
    padding: 12px;
  }

  .document-letter {
    padding: 16px;
  }

  .details-header {
    padding: 10px 12px;
  }

  .details-header h2 {
    font-size: 1rem;
  }

  .candidate-info-box {
    padding: 12px;
  }

  .info-table td {
    padding: 6px 0;
  }

  .info-table td:first-child {
    width: 45%;
    font-size: 12px;
  }

  .info-table td:last-child {
    padding-left: 8px;
    font-size: 12px;
  }

  .letter-content {
    font-size: 13px;
  }

  .letter-content p {
    margin: 15px 0;
  }

  .company-name {
    font-size: 16px;
  }

  .logo-placeholder {
    width: 30px;
    height: 30px;
    font-size: 12px;
  }
}
</style>