<template>
  <div class="header">
    <div class="header-content">
      <div class="nav-tabs">
        <div 
          v-for="tab in tabs" 
          :key="tab.id"
          :class="['nav-tab', { active: tab.active }]"
          @click="selectTab(tab.id)"
        >
          <span>{{ tab.name }}</span>
        </div>
      </div>
     
 <div class="profile-icon">
        <v-text-field
          v-model="search"
          placeholder="Search..."
          prepend-inner-icon="mdi-magnify"
          variant="outlined"
          density="compact"
          hide-details
          clearable
          class="nav-search"
        />
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const search = defineModel("search") 

const tabs = ref([
  { id: 1, name: 'Document Generator', active: true },
  { id: 2, name: 'Configuration', active: false }
])

const selectTab = (tabId) => {
  tabs.value.forEach(tab => {
    tab.active = tab.id === tabId
  })
}




</script>

<style scoped>
.header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 20px;
  margin-bottom: 12px;
  position: fixed;
   top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-tabs {
  display: flex;
  gap: 20px;
}

.nav-tab {
  padding: 6px 10px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 1px solid transparent;
  transition: all 0.3s;
}

.nav-tab:hover {
  color: #111;
}

.nav-tab.active {
  color: #26f5b0;
  border-bottom: 2px solid #26f5b0;
}

.profile-icon {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.search-icon {
  width: 20px;
  height: 20px;
  color: #555;
  transition: color 0.3s;
}

.search-icon:hover {
  color: #1976d2;
}
</style>
