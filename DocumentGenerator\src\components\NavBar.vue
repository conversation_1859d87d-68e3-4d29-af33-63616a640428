<template>
  <div class="header">
    <div class="header-content">
      <div class="nav-tabs">
        <div 
          v-for="tab in tabs" 
          :key="tab.id"
          :class="['nav-tab', { active: tab.active }]"
          @click="selectTab(tab.id)"
        >
          <span>{{ tab.name }}</span>
        </div>
      </div>
     
 <div class="profile-icon">
        <v-text-field
          v-model="search"
          placeholder="Search..."
          prepend-inner-icon="mdi-magnify"
          variant="outlined"
          density="compact"
          hide-details
          clearable
          class="nav-search"
        />
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const search = defineModel("search") 

const tabs = ref([
  { id: 1, name: 'Document Generator', active: true },
  { id: 2, name: 'Configuration', active: false }
])

const selectTab = (tabId) => {
  tabs.value.forEach(tab => {
    tab.active = tab.id === tabId
  })
}




</script>

<style scoped>
.header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 20px;
  margin-bottom: 12px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-sizing: border-box;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.nav-tabs {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.nav-tab {
  padding: 6px 10px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 1px solid transparent;
  transition: all 0.3s;
}

.nav-tab:hover {
  color: #111;
}

.nav-tab.active {
  color: #26f5b0;
  border-bottom: 2px solid #26f5b0;
}

.profile-icon {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.search-icon {
  width: 20px;
  height: 20px;
  color: #555;
  transition: color 0.3s;
}

.search-icon:hover {
  color: #1976d2;
}

/* Tablet styles */
@media (max-width: 1024px) {
  .header {
    padding: 10px 16px;
  }

  .nav-tabs {
    gap: 16px;
  }

  .nav-tab {
    padding: 6px 8px;
    font-size: 13px;
  }

  :deep(.nav-search .v-field) {
    min-width: 200px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .header {
    padding: 8px 12px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .nav-tabs {
    justify-content: center;
    gap: 12px;
  }

  .nav-tab {
    padding: 8px 12px;
    font-size: 12px;
    flex: 1;
    text-align: center;
    min-width: 0;
  }

  .profile-icon {
    justify-content: center;
  }

  :deep(.nav-search) {
    width: 100%;
  }

  :deep(.nav-search .v-field) {
    min-width: auto;
    width: 100%;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .header {
    padding: 6px 8px;
  }

  .nav-tabs {
    gap: 8px;
  }

  .nav-tab {
    padding: 6px 8px;
    font-size: 11px;
  }

  :deep(.nav-search .v-field__input) {
    font-size: 14px;
  }

  :deep(.nav-search .v-field__prepend-inner) {
    padding-inline-end: 8px;
  }
}
</style>
