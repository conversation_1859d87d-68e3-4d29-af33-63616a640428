<template>
  <div class="document-page-container">
    <NavBar v-model:search="search" />

    <div class="page-content">
      <DataTable
        v-if="!selectedItem"
        :items="items"
        :search="search"
        @row-selected="handleRowSelected"
        @refresh="handleRefresh"
        @add-new="handleAddNew"
      />

      <DocumentView
        v-if="selectedItem"
        :item="selectedItem"
        @close="handleCloseDocument"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import NavBar from '../components/NavBar.vue'
import DataTable from '../components/DataTable.vue'
import DocumentView from '../components/DocumentView.vue'

const search = ref("") 

const items = ref([
  {
    employee: '<PERSON>',
    employeeId: 'EMP001',
    candidate: '<PERSON>',
    candidateId: 'CAN001',
    document: 'Offer Letter',
    status: 'Pending',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Admin',
    department: 'Alpha',
    mobile: '066665 07737'
  },
  {
    employee: '<PERSON>',
    employeeId: 'EMP002',
    candidate: '<PERSON>',
    candidateId: 'CAN002',
    document: 'Employment Agreement',
    status: 'Completed',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Developer',
    department: 'Tech',
    mobile: '123456 7890'
  },
  {
    employee: 'Alice Brown',
    employeeId: 'EMP003',
    candidate: 'Robert Davis',
    candidateId: 'CAN003',
    document: 'Contract',
    status: 'Draft',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'Designer',
    department: 'Design',
    mobile: '999888 7777'
  },
  {
    employee: 'Sarah Wilson',
    employeeId: 'EMP004',
    candidate: 'David Thompson',
    candidateId: 'CAN004',
    document: 'Offer Letter',
    status: 'Pending',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Analyst',
    department: 'Finance',
    mobile: '555123 4567'
  },
  {
    employee: 'Mark Johnson',
    employeeId: 'EMP005',
    candidate: 'Lisa Anderson',
    candidateId: 'CAN005',
    document: 'Employment Agreement',
    status: 'Completed',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Marketing Manager',
    department: 'Marketing',
    mobile: '777888 9999'
  },
  {
    employee: 'Emily Davis',
    employeeId: 'EMP006',
    candidate: 'James Wilson',
    candidateId: 'CAN006',
    document: 'Contract',
    status: 'In Review',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Software Engineer',
    department: 'Engineering',
    mobile: '444555 6666'
  },
  {
    employee: 'Chris Miller',
    employeeId: 'EMP007',
    candidate: 'Amanda Rodriguez',
    candidateId: 'CAN007',
    document: 'Offer Letter',
    status: 'Pending',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'UX Designer',
    department: 'Design',
    mobile: '333444 5555'
  },
  {
    employee: 'Jennifer Lee',
    employeeId: 'EMP008',
    candidate: 'Kevin Brown',
    candidateId: 'CAN008',
    document: 'Employment Agreement',
    status: 'Completed',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Project Manager',
    department: 'Operations',
    mobile: '222333 4444'
  },
  {
    employee: 'Daniel Garcia',
    employeeId: 'EMP009',
    candidate: 'Michelle Taylor',
    candidateId: 'CAN009',
    document: 'Contract',
    status: 'Draft',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'Business Analyst',
    department: 'Strategy',
    mobile: '111222 3333'
  },
  {
    employee: 'Ryan Martinez',
    employeeId: 'EMP010',
    candidate: 'Rachel Green',
    candidateId: 'CAN010',
    document: 'Offer Letter',
    status: 'Pending',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Data Scientist',
    department: 'Analytics',
    mobile: '999000 1111'
  },
  {
    employee: 'Laura Thompson',
    employeeId: 'EMP011',
    candidate: 'Steven Clark',
    candidateId: 'CAN011',
    document: 'Employment Agreement',
    status: 'In Review',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'QA Engineer',
    department: 'Quality',
    mobile: '888999 0000'
  },
  {
    employee: 'Michael White',
    employeeId: 'EMP012',
    candidate: 'Nicole Harris',
    candidateId: 'CAN012',
    document: 'Contract',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Sales Representative',
    department: 'Sales',
    mobile: '777666 5555'
  },
  {
    employee: 'Ashley Moore',
    employeeId: 'EMP013',
    candidate: 'Christopher Lee',
    candidateId: 'CAN013',
    document: 'Offer Letter',
    status: 'Pending',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'DevOps Engineer',
    department: 'Infrastructure',
    mobile: '666555 4444'
  },
  {
    employee: 'Brian Johnson',
    employeeId: 'EMP014',
    candidate: 'Samantha Davis',
    candidateId: 'CAN014',
    document: 'Employment Agreement',
    status: 'Draft',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Content Writer',
    department: 'Content',
    mobile: '555444 3333'
  },
  {
    employee: 'Jessica Wilson',
    employeeId: 'EMP015',
    candidate: 'Matthew Miller',
    candidateId: 'CAN015',
    document: 'Contract',
    status: 'In Review',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'Security Analyst',
    department: 'Security',
    mobile: '444333 2222'
  },
  {
    employee: 'Thomas Anderson',
    employeeId: 'EMP016',
    candidate: 'Jessica Martinez',
    candidateId: 'CAN016',
    document: 'Offer Letter',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Product Manager',
    department: 'Product',
    mobile: '333222 1111'
  },
  {
    employee: 'Maria Rodriguez',
    employeeId: 'EMP017',
    candidate: 'Joshua Garcia',
    candidateId: 'CAN017',
    document: 'Employment Agreement',
    status: 'Pending',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Technical Writer',
    department: 'Documentation',
    mobile: '222111 9999'
  },
  {
    employee: 'Andrew Taylor',
    employeeId: 'EMP018',
    candidate: 'Amy Johnson',
    candidateId: 'CAN018',
    document: 'Contract',
    status: 'Draft',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'HR Coordinator',
    department: 'HR',
    mobile: '111999 8888'
  },
  {
    employee: 'Rebecca Brown',
    employeeId: 'EMP019',
    candidate: 'Daniel Williams',
    candidateId: 'CAN019',
    document: 'Offer Letter',
    status: 'In Review',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Frontend Developer',
    department: 'Development',
    mobile: '999888 7777'
  },
  {
    employee: 'Kevin Davis',
    employeeId: 'EMP020',
    candidate: 'Sarah Thompson',
    candidateId: 'CAN020',
    document: 'Employment Agreement',
    status: 'Completed',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Operations Manager',
    department: 'Operations',
    mobile: '888777 6666'
  },
  {
    employee: 'Michelle Wilson',
    employeeId: 'EMP021',
    candidate: 'Robert Johnson',
    candidateId: 'CAN021',
    document: 'Contract',
    status: 'Pending',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'Backend Developer',
    department: 'Development',
    mobile: '777666 5555'
  },
  {
    employee: 'David Miller',
    employeeId: 'EMP022',
    candidate: 'Jennifer Brown',
    candidateId: 'CAN022',
    document: 'Offer Letter',
    status: 'Draft',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Graphic Designer',
    department: 'Creative',
    mobile: '666555 4444'
  },
  {
    employee: 'Lisa Garcia',
    employeeId: 'EMP023',
    candidate: 'Michael Davis',
    candidateId: 'CAN023',
    document: 'Employment Agreement',
    status: 'In Review',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'System Administrator',
    department: 'IT',
    mobile: '555444 3333'
  },
  {
    employee: 'Jason Rodriguez',
    employeeId: 'EMP024',
    candidate: 'Laura Wilson',
    candidateId: 'CAN024',
    document: 'Contract',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Financial Analyst',
    department: 'Finance',
    mobile: '444333 2222'
  },
  {
    employee: 'Amanda Taylor',
    employeeId: 'EMP025',
    candidate: 'Brian Martinez',
    candidateId: 'CAN025',
    document: 'Offer Letter',
    status: 'Pending',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Mobile Developer',
    department: 'Mobile',
    mobile: '333222 1111'
  },
  {
    employee: 'Jonathan Lee',
    employeeId: 'EMP026',
    candidate: 'Stephanie Garcia',
    candidateId: 'CAN026',
    document: 'Employment Agreement',
    status: 'Draft',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Research Analyst',
    department: 'Research',
    mobile: '222111 9999'
  },
  {
    employee: 'Stephanie Clark',
    employeeId: 'EMP027',
    candidate: 'Anthony Rodriguez',
    candidateId: 'CAN027',
    document: 'Contract',
    status: 'In Review',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'Database Administrator',
    department: 'Database',
    mobile: '111999 8888'
  },
  {
    employee: 'Joshua White',
    employeeId: 'EMP028',
    candidate: 'Melissa Thompson',
    candidateId: 'CAN028',
    document: 'Offer Letter',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Social Media Manager',
    department: 'Social Media',
    mobile: '999888 7777'
  },
  {
    employee: 'Nicole Harris',
    employeeId: 'EMP029',
    candidate: 'Justin Wilson',
    candidateId: 'CAN029',
    document: 'Employment Agreement',
    status: 'Pending',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Cloud Engineer',
    department: 'Cloud',
    mobile: '888777 6666'
  },
  {
    employee: 'Brandon Moore',
    employeeId: 'EMP030',
    candidate: 'Kimberly Davis',
    candidateId: 'CAN030',
    document: 'Contract',
    status: 'Draft',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Training Coordinator',
    department: 'Training',
    mobile: '777666 5555'
  },
  {
    employee: 'Tyler Johnson',
    employeeId: 'EMP031',
    candidate: 'Heather Miller',
    candidateId: 'CAN031',
    document: 'Offer Letter',
    status: 'In Review',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Supply Chain Analyst',
    department: 'Supply Chain',
    mobile: '666555 4444'
  },
  {
    employee: 'Crystal Anderson',
    employeeId: 'EMP032',
    candidate: 'Nathan Brown',
    candidateId: 'CAN032',
    document: 'Employment Agreement',
    status: 'Completed',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Network Engineer',
    department: 'Network',
    mobile: '555444 3333'
  },
  {
    employee: 'Jacob Taylor',
    employeeId: 'EMP033',
    candidate: 'Christina Garcia',
    candidateId: 'CAN033',
    document: 'Contract',
    status: 'Pending',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'UI Designer',
    department: 'Design',
    mobile: '444333 2222'
  },
  {
    employee: 'Samantha Martinez',
    employeeId: 'EMP034',
    candidate: 'Eric Rodriguez',
    candidateId: 'CAN034',
    document: 'Offer Letter',
    status: 'Draft',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Marketing Specialist',
    department: 'Marketing',
    mobile: '333222 1111'
  },
  {
    employee: 'Anthony Wilson',
    employeeId: 'EMP035',
    candidate: 'Megan Wilson',
    candidateId: 'CAN035',
    document: 'Employment Agreement',
    status: 'In Review',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Customer Success Manager',
    department: 'Customer Success',
    mobile: '222111 9999'
  },
  {
    employee: 'Monica Davis',
    employeeId: 'EMP036',
    candidate: 'Alexander Thompson',
    candidateId: 'CAN036',
    document: 'Contract',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Sales Manager',
    department: 'Sales',
    mobile: '111999 8888'
  },
  {
    employee: 'Gregory Brown',
    employeeId: 'EMP037',
    candidate: 'Victoria Martinez',
    candidateId: 'CAN037',
    document: 'Offer Letter',
    status: 'Pending',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Technical Lead',
    department: 'Engineering',
    mobile: '999888 7777'
  },
  {
    employee: 'Rachel Johnson',
    employeeId: 'EMP038',
    candidate: 'Tyler Davis',
    candidateId: 'CAN038',
    document: 'Employment Agreement',
    status: 'Draft',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Product Designer',
    department: 'Product',
    mobile: '888777 6666'
  },
  {
    employee: 'Aaron Miller',
    employeeId: 'EMP039',
    candidate: 'Danielle Garcia',
    candidateId: 'CAN039',
    document: 'Contract',
    status: 'In Review',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'Business Development Manager',
    department: 'Business Development',
    mobile: '777666 5555'
  },
  {
    employee: 'Cynthia Rodriguez',
    employeeId: 'EMP040',
    candidate: 'Jacob Anderson',
    candidateId: 'CAN040',
    document: 'Offer Letter',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Software Architect',
    department: 'Architecture',
    mobile: '666555 4444'
  },
  {
    employee: 'Patrick Wilson',
    employeeId: 'EMP041',
    candidate: 'Courtney Taylor',
    candidateId: 'CAN041',
    document: 'Employment Agreement',
    status: 'Pending',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Compliance Officer',
    department: 'Compliance',
    mobile: '555444 3333'
  },
  {
    employee: 'Catherine Lee',
    employeeId: 'EMP042',
    candidate: 'Sean Thomas',
    candidateId: 'CAN042',
    document: 'Contract',
    status: 'Draft',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Data Engineer',
    department: 'Data',
    mobile: '444333 2222'
  },
  {
    employee: 'Dennis Garcia',
    employeeId: 'EMP043',
    candidate: 'Brittany Johnson',
    candidateId: 'CAN043',
    document: 'Offer Letter',
    status: 'In Review',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Event Coordinator',
    department: 'Events',
    mobile: '333222 1111'
  },
  {
    employee: 'Frank Martinez',
    employeeId: 'EMP044',
    candidate: 'Ashley Brown',
    candidateId: 'CAN044',
    document: 'Employment Agreement',
    status: 'Completed',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Account Manager',
    department: 'Accounts',
    mobile: '222111 9999'
  },
  {
    employee: 'Kelly Thompson',
    employeeId: 'EMP045',
    candidate: 'Kenneth Wilson',
    candidateId: 'CAN045',
    document: 'Contract',
    status: 'Pending',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'Machine Learning Engineer',
    department: 'AI/ML',
    mobile: '111999 8888'
  },
  {
    employee: 'Harold Davis',
    employeeId: 'EMP046',
    candidate: 'Tiffany Miller',
    candidateId: 'CAN046',
    document: 'Offer Letter',
    status: 'Draft',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'HR Manager',
    department: 'HR',
    mobile: '999888 7777'
  },
  {
    employee: 'Vanessa Wilson',
    employeeId: 'EMP047',
    candidate: 'Brandon Johnson',
    candidateId: 'CAN047',
    document: 'Employment Agreement',
    status: 'In Review',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Operations Analyst',
    department: 'Operations',
    mobile: '888777 6666'
  },
  {
    employee: 'Jerry Brown',
    employeeId: 'EMP048',
    candidate: 'Christina Martinez',
    candidateId: 'CAN048',
    document: 'Contract',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Legal Advisor',
    department: 'Legal',
    mobile: '777666 5555'
  },
  {
    employee: 'Angela Rodriguez',
    employeeId: 'EMP049',
    candidate: 'Jason Garcia',
    candidateId: 'CAN049',
    document: 'Offer Letter',
    status: 'Pending',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'SEO Specialist',
    department: 'SEO',
    mobile: '666555 4444'
  },
  {
    employee: 'Edward Taylor',
    employeeId: 'EMP050',
    candidate: 'Molly Davis',
    candidateId: 'CAN050',
    document: 'Employment Agreement',
    status: 'Draft',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Recruiter',
    department: 'Talent Acquisition',
    mobile: '555444 3333'
  },
  {
    employee: 'Natalie Wilson',
    employeeId: 'EMP051',
    candidate: 'Ethan Brown',
    candidateId: 'CAN051',
    document: 'Contract',
    status: 'In Review',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'System Analyst',
    department: 'Systems',
    mobile: '444333 2222'
  },
  {
    employee: 'Victor Johnson',
    employeeId: 'EMP052',
    candidate: 'Hannah Thompson',
    candidateId: 'CAN052',
    document: 'Offer Letter',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Innovation Manager',
    department: 'Innovation',
    mobile: '333222 1111'
  },
  {
    employee: 'Sophia Martinez',
    employeeId: 'EMP053',
    candidate: 'Benjamin Garcia',
    candidateId: 'CAN053',
    document: 'Employment Agreement',
    status: 'Pending',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'IT Consultant',
    department: 'Consulting',
    mobile: '222111 9999'
  },
  {
    employee: 'Dylan Davis',
    employeeId: 'EMP054',
    candidate: 'Olivia Wilson',
    candidateId: 'CAN054',
    document: 'Contract',
    status: 'Draft',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Creative Director',
    department: 'Creative',
    mobile: '111999 8888'
  },
  {
    employee: 'Natalie Garcia',
    employeeId: 'EMP055',
    candidate: 'Zachary Taylor',
    candidateId: 'CAN055',
    document: 'Offer Letter',
    status: 'In Review',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Automation Engineer',
    department: 'Automation',
    mobile: '999888 7777'
  },
  {
    employee: 'Ethan Wilson',
    employeeId: 'EMP056',
    candidate: 'Sophie Martinez',
    candidateId: 'CAN056',
    document: 'Employment Agreement',
    status: 'Completed',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Full Stack Developer',
    department: 'Development',
    mobile: '888777 6666'
  },
  {
    employee: 'Isabella Brown',
    employeeId: 'EMP057',
    candidate: 'Logan Rodriguez',
    candidateId: 'CAN057',
    document: 'Contract',
    status: 'Pending',
    author: 'Legal Team',
    email: '<EMAIL>',
    designation: 'Support Engineer',
    department: 'Support',
    mobile: '777666 5555'
  },
  {
    employee: 'Matthew Davis',
    employeeId: 'EMP058',
    candidate: 'Ava Johnson',
    candidateId: 'CAN058',
    document: 'Offer Letter',
    status: 'Draft',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Recruitment Specialist',
    department: 'HR',
    mobile: '666555 4444'
  },
  {
    employee: 'Chloe Wilson',
    employeeId: 'EMP059',
    candidate: 'William Miller',
    candidateId: 'CAN059',
    document: 'Employment Agreement',
    status: 'In Review',
    author: 'Legal Department',
    email: '<EMAIL>',
    designation: 'Strategic Planner',
    department: 'Strategy',
    mobile: '555444 3333'
  },
  {
    employee: 'Owen Martinez',
    employeeId: 'EMP060',
    candidate: 'Ella Davis',
    candidateId: 'CAN060',
    document: 'Contract',
    status: 'Completed',
    author: 'HR Department',
    email: '<EMAIL>',
    designation: 'Chief of Staff',
    department: 'Management',
    mobile: '444333 2222'
  }
])



const selectedItem = ref(null)

const handleRowSelected = (item) => {
  selectedItem.value = item
}

const handleCloseDocument = () => {
  selectedItem.value = null
}

const handleRefresh = () => {
  console.log('Refresh triggered')
  selectedItem.value = null
  // Add your refresh logic here
}

const handleAddNew = () => {
  console.log('Add New clicked')
  // Add your add new logic here
}
</script>

<style scoped>
.document-page-container {
  min-height: 100vh;
  background-color: #F9F8F5;
  width: 100%;
  box-sizing: border-box;
}

.page-content {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 0;
}

/* Tablet styles */
@media (max-width: 1024px) {
  .page-content {
    padding: 0 8px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .document-page-container {
    padding: 0;
  }

  .page-content {
    padding: 0 4px;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .page-content {
    padding: 0 2px;
  }
}
</style>

