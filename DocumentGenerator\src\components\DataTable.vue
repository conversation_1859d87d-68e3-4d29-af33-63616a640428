<template>
  <div class="data-table-container">
    <!-- Actions row -->
    <div
      class="d-flex justify-end align-center mb-3 mr-10 mt-6 gap-3 action-bar flex-wrap"
    >
      <!-- Refresh -->
      <v-btn
        variant="text"
        size="small"
        class="action-btn refresh-btn"
        @click="handleRefresh"
      >
        <v-icon size="30">mdi-refresh</v-icon>
      </v-btn>

      <!-- Add New -->
      <v-btn
        color="#58f0bd"
        variant="flat"
        size="large"
        class="action-btn add-btn"
        @click="handleAddNew"
      >
        <v-icon size="28" class="mr-2">fas fa-plus</v-icon>
        <div class="btn-add-new">ADD NEW</div>
      </v-btn>
    </div>

    <!-- Table (responsive wrapper) -->
    <div class="table-responsive">
      <v-data-table
        :headers="headers"
        :items="items"
        :search="search"
        v-model:page="page"
        :items-per-page="itemsPerPage"
        :items-per-page-options="[50, 100, -1]"
        class="elevation-1"
        fixed-header
        :height="$vuetify.display.mdAndUp ? '600px' : '400px'"
        @click:row="handleRowClick"
      >
        <!-- Employee -->
        <template #item.employee="{ item }">
          <div>{{ item.employee }}</div>
          <div class="text-grey text-caption">{{ item.employeeId }}</div>
        </template>

        <!-- Candidate -->
        <template #item.candidate="{ item }">
          <div>{{ item.candidate }}</div>
          <div class="text-grey text-caption">{{ item.candidateId }}</div>
        </template>

        <!-- Signatories -->
        <template #item.signatories="{ item }">
          <div class="signatories">
            <span class="initial success">{{ getInitials(item.employee) }}</span>
            <span class="initial success">{{ getInitials(item.candidate) }}</span>
          </div>
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <v-menu>
            <template #activator="{ props }">
              <v-btn v-bind="props" icon variant="text" size="small">
                <v-icon size="20">mdi-dots-vertical</v-icon>
              </v-btn>
            </template>

            <v-list class="actions-menu">
              <v-list-item @click="viewPdf(item)">
                <template #prepend>
                  <v-icon size="20" color="red">mdi-file-pdf-box</v-icon>
                </template>
                <v-list-item-title>PDF</v-list-item-title>
              </v-list-item>

              <v-list-item @click="printDoc(item)">
                <template #prepend>
                  <v-icon size="20">mdi-printer</v-icon>
                </template>
                <v-list-item-title>Print</v-list-item-title>
              </v-list-item>

              <v-list-item @click="sendMail(item)">
                <template #prepend>
                  <v-icon size="20" color="green">mdi-email</v-icon>
                </template>
                <v-list-item-title>Send Mail</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </template>
      </v-data-table>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"

const props = defineProps({
  items: {
    type: Array,
    required: true,
  },
  search: String,
})

const emit = defineEmits(["row-selected", "refresh", "add-new"])

const getInitials = (fullName) => {
  if (!fullName) return ""
  const parts = fullName.trim().split(" ")
  if (parts.length === 1) {
    return parts[0][0].toUpperCase()
  }
  return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase()
}

const headers = ref([
  { title: "Employee Name", key: "employee" },
  { title: "Candidate Name", key: "candidate" },
  { title: "Document Name", key: "document" },
  { title: "Signatories", key: "signatories" },
  { title: "Status", key: "status" },
  { title: "Document Author", key: "author" },
  { title: "Actions", key: "actions", sortable: false },
])

const page = ref(1)
const itemsPerPage = ref(50)

const handleRowClick = (event, { item }) => {
  emit("row-selected", item)
}

const handleRefresh = () => {
  emit("refresh")
}

const handleAddNew = () => {
  emit("add-new")
}

const viewPdf = (item) => {
  console.log("View PDF for:", item)
}

const printDoc = (item) => {
  console.log("Print doc for:", item)
  window.print()
}

const sendMail = (item) => {
  console.log("Send mail for:", item)
  window.location.href = `mailto:${item.email}?subject=Regarding ${item.document}`
}
</script>

<style scoped>
.data-table-container {
  width: 100%;
  margin-top: 60px;
}

.table-responsive {
  width: 100%;
  overflow-x: auto;
}

.action-bar {
  gap: 12px;
}

.action-btn i,
.action-btn .v-icon {
  font-size: 16px;
  color: #ffffff;
}

.add-btn:hover {
  background-color: #2e7d32;
  color: #fff;
}

.btn-add-new {
  color: #ffffff;
}

.v-table {
  width: 100%;
}

:deep(.v-data-table tbody tr) {
  transition: all 0.1s ease;
  cursor: pointer;
  height: 60px;
}

:deep(.v-data-table tbody tr:hover) {
  background: #fafbff;
  box-shadow: 0 4px 10px rgba(0.15, 0.15, 0.15, 0.15);
  transform: translateY(0px);
  position: relative;
  z-index: 2;
}

:deep(.v-data-table tbody td) {
  padding: 12px 16px;
  vertical-align: middle;
  background: #fff;
  color: #58f0bd;
}

:deep(.v-data-table tbody tr + tr td) {
  border-top: 8px solid transparent;
}

:deep(.v-table__wrapper) {
  overflow-y: auto !important;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

:deep(.v-table__wrapper::-webkit-scrollbar) {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.text-grey {
  color: #666;
}

.text-caption {
  font-size: 0.85em;
  margin-top: 2px;
}

.signatories {
  display: flex;
  gap: 6px;
}

.initial {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  font-weight: bold;
  font-size: 0.8rem;
  text-transform: uppercase;
  color: #fff;
}

.initial.success {
  background-color: #4caf50;
}

:deep(.v-data-table tbody td:nth-child(5)) {
  color: inherit !important;
}

:deep(.v-btn.icon) {
  color: #666;
}

:deep(.v-btn.icon:hover) {
  background: #f5f5f5;
}

.actions-menu {
  min-width: 160px;
}

.actions-menu .v-list-item {
  padding: 10px 16px;
}

.refresh-btn .v-icon {
  color: #58f0bd;
}

/* 📱 Responsive adjustments */
@media (max-width: 768px) {
  .btn-add-new {
    font-size: 0.8rem;
  }

  .initial {
    width: 28px;
    height: 28px;
    font-size: 0.7rem;
  }

  :deep(.v-data-table tbody td) {
    padding: 8px;
    font-size: 0.8rem;
  }
}
</style>
